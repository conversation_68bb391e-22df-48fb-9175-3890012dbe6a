#!/usr/bin/env python3
"""
iICrawlerMCP客户端演示脚本

演示如何使用MCP客户端连接到iICrawlerMCP服务器并执行各种网页任务。

使用方法:
    1. 先启动MCP服务器: python scripts/start_mcp_server.py
    2. 在另一个终端运行: python scripts/mcp_client_demo.py
"""

import asyncio
import sys
import os
from pathlib import Path
from dotenv import load_dotenv

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 加载环境变量
load_dotenv(project_root / ".env")

try:
    from mcp import ClientSession, StdioServerParameters
    from mcp.client.stdio import stdio_client
except ImportError:
    print("缺少MCP客户端依赖，请安装: pip install mcp", file=sys.stderr)
    sys.exit(1)


class MCPClientDemo:
    """MCP客户端演示类"""

    def __init__(self):
        self.session = None
        self.read_stream = None
        self.write_stream = None
        self.stdio_context = None
    
    async def start_server(self):
        """启动MCP服务器"""
        print("启动MCP服务器...")

        # 使用stdio_client连接到服务器
        server_script = project_root / "scripts" / "start_mcp_server.py"
        server_params = StdioServerParameters(
            command=sys.executable,
            args=[str(server_script)]
        )

        self.stdio_context = stdio_client(server_params)
        self.read_stream, self.write_stream = await self.stdio_context.__aenter__()
        print("MCP服务器启动成功")
    
    async def connect_client(self):
        """连接MCP客户端"""
        print("连接MCP客户端...")

        # 创建客户端会话
        self.session = ClientSession(self.read_stream, self.write_stream)
        print("正在初始化客户端会话...")

        # 添加超时机制
        try:
            await asyncio.wait_for(self.session.initialize(), timeout=30.0)
            print("MCP客户端连接成功")
        except asyncio.TimeoutError:
            print("客户端初始化超时")
            raise
    
    async def demo_list_tools(self):
        """演示：列出可用工具"""
        print("\n 演示1: 列出可用工具")
        print("-" * 30)
        
        tools = await self.session.list_tools()
        print(f"可用工具数量: {len(tools.tools)}")
        
        for tool in tools.tools:
            print(f"{tool.name}: {tool.description}")

    async def demo_simple_task(self):
        """演示：执行简单任务"""
        print("\nDEMO3")
        print("-" * 30)

        task = "OPEN baidu.com,take a screen shot"
        print(f"task: {task}")

        print("正在调用工具...")
        result = await self.session.call_tool(
            "intelligent_web_task",
            {"task_description": task}
        )

        print(f"result: {result.content[0].text}")

    async def demo_complex_task(self):
        """演示：执行复杂任务"""
        print("\n演示5: 执行复杂网页任务")
        print("-" * 30)
        
        task = "打开httpbin.org/forms/post，找到表单，填写一些测试数据，然后截图"
        print(f"任务: {task}")
        
        result = await self.session.call_tool(
            "intelligent_web_task", 
            {"task_description": task}
        )
        
        print(f"执行结果: {result.content[0].text}")
    
    async def demo_cleanup(self):
        """演示：清理资源"""
        print("\n演示6: 清理浏览器资源")
        print("-" * 30)
        
        result = await self.session.call_tool("cleanup_browser", {})
        print(f"清理结果: {result.content[0].text}")
    
    async def cleanup(self):
        """清理资源"""
        print("清理资源...")

        if self.session:
            try:
                # 清理浏览器资源
                await self.session.call_tool("cleanup_browser", {})
            except Exception as e:
                print(f"清理浏览器资源时出错: {e}")

        if self.stdio_context:
            try:
                await self.stdio_context.__aexit__(None, None, None)
            except Exception as e:
                print(f"关闭stdio连接时出错: {e}")

        print("资源清理完成")
    
    async def run_all_demos(self):
        """运行所有演示"""
        try:
            await self.start_server()
            await self.connect_client()
            
            # 运行各种演示
            await self.demo_list_tools()
            await self.demo_browser_status()
            await self.demo_simple_task()
            await self.demo_screenshot()
            await self.demo_complex_task()
            await self.demo_cleanup()
            
            print("所有演示完成!")
            
        except Exception as e:
            print(f"演示失败: {e}")
            raise
        finally:
            await self.cleanup()


def check_environment():
    """检查环境配置"""
    print("检查环境配置...")

    # 检查必需的环境变量
    required_env = ["OPENAI_API_KEY"]
    missing_env = []

    for env_var in required_env:
        if not os.getenv(env_var):
            missing_env.append(env_var)

    if missing_env:
        print(f"缺少必需的环境变量: {', '.join(missing_env)}")
        print("请检查.env文件中是否设置了以下变量:")
        for env_var in missing_env:
            print(f"  {env_var}=your_value")
        return False

    # 显示当前配置
    print("环境变量检查通过:")
    print(f"  OPENAI_API_KEY: {'已设置' if os.getenv('OPENAI_API_KEY') else '未设置'}")
    if os.getenv("OPENAI_API_BASE"):
        print(f"  OPENAI_API_BASE: {os.getenv('OPENAI_API_BASE')}")
    if os.getenv("OPENAI_MODEL"):
        print(f"  OPENAI_MODEL: {os.getenv('OPENAI_MODEL')}")

    return True


async def main():
    """主函数"""
    print("HELLO WORLD")
    # print("iICrawlerMCP客户端演示")
    print("=" * 50)

    # 检查环境
    if not check_environment():
        sys.exit(1)

    demo = MCPClientDemo()
    try:
        print("步骤1: 启动MCP服务器...")
        await demo.start_server()
        print("步骤2: 连接MCP客户端...")
        await demo.connect_client()
        print("步骤3: 执行简单任务...")
        await demo.demo_simple_task()
        print("演示完成!")
    except KeyboardInterrupt:
        print("演示被用户中断")
    except Exception as e:
        print(f"演示失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await demo.cleanup()


if __name__ == "__main__":
    asyncio.run(main())
