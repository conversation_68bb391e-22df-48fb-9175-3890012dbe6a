#!/usr/bin/env python3
"""
LangChain + MCP 最简单方案
使用 langchain-mcp-adapters 包来集成 iICrawlerMCP 服务器
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 加载环境变量
from dotenv import load_dotenv
load_dotenv()

async def test_langchain_mcp():
    """测试LangChain + MCP集成"""
    print("=== LangChain + MCP 最简单方案测试 ===")
    
    try:
        # 导入必要的包
        from langchain_mcp_adapters.client import MultiServerMCPClient
        from langgraph.prebuilt import create_react_agent
        from langchain_openai import ChatOpenAI
        
        print("✓ 成功导入LangChain MCP适配器")
        
        # 初始化OpenAI模型
        model = ChatOpenAI(
            model=os.getenv("OPENAI_MODEL", "gpt-4o"),
            api_key=os.getenv("OPENAI_API_KEY"),
            base_url=os.getenv("OPENAI_API_BASE")
        )
        print("✓ 成功初始化OpenAI模型")
        
        # 配置MCP服务器
        server_script = project_root / "scripts" / "start_mcp_server.py"
        
        client = MultiServerMCPClient({
            "iicrawler": {
                "command": sys.executable,
                "args": [str(server_script)],
                "transport": "stdio",
            }
        })
        print("✓ 成功配置MCP客户端")
        
        # 获取MCP工具
        print("正在获取MCP工具...")
        tools = await client.get_tools()
        print(f"✓ 成功获取 {len(tools)} 个工具:")
        for tool in tools:
            print(f"  - {tool.name}: {tool.description}")
        
        # 创建LangGraph代理
        agent = create_react_agent(model, tools)
        print("✓ 成功创建LangGraph代理")
        
        # 测试简单任务
        print("\n开始执行任务: 打开百度并截图")
        response = await agent.ainvoke({
            "messages": [{"role": "user", "content": "OPEN baidu.com,take a screen shot"}]
        })
        
        print("✓ 任务执行完成!")
        print(f"最终响应: {response['messages'][-1].content}")
        
        return True
        
    except ImportError as e:
        print(f"✗ 导入错误: {e}")
        print("请安装必要的包:")
        print("pip install langchain-mcp-adapters")
        print("pip install langchain-openai")
        print("pip install langgraph")
        return False
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_environment():
    """检查环境配置"""
    print("检查环境配置...")
    
    required_vars = ["OPENAI_API_KEY", "OPENAI_API_BASE", "OPENAI_MODEL"]
    missing_vars = []
    
    for var in required_vars:
        value = os.getenv(var)
        if value:
            if var == "OPENAI_API_KEY":
                print(f"  {var}: 已设置")
            else:
                print(f"  {var}: {value}")
        else:
            missing_vars.append(var)
    
    if missing_vars:
        print(f"✗ 缺少环境变量: {missing_vars}")
        return False
    
    print("✓ 环境配置检查通过")
    return True

if __name__ == "__main__":
    print("LangChain + MCP 最简单方案")
    print("=" * 50)
    
    if not check_environment():
        sys.exit(1)
    
    success = asyncio.run(test_langchain_mcp())
    sys.exit(0 if success else 1)
