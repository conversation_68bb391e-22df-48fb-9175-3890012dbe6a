# Core dependencies
langchain>=0.1.0
langchain-community>=0.0.20
langchain-openai>=0.0.5
playwright>=1.40.0
python-dotenv>=1.0.0
# MCP (Model Context Protocol) support
mcp>=1.0.0
pytest
langchain_mcp_adapters
langgraph
pydantic>=2.0.0
pytest-asyncio
# Development dependencies (optional)
# Install with: pip install -r requirements-dev.txt
# pytest>=7.0.0
# pytest-asyncio>=0.21.0
# black>=23.0.0
# flake8>=6.0.0
# mypy>=1.0.0